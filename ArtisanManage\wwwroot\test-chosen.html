<!DOCTYPE html>
<html>
<head>
    <title>Chosen 测试页面</title>
    <link rel="stylesheet" href="ChosenSelect/chosen.css" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="ChosenSelect/chosen.jquery.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        h3 {
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Chosen 多选下拉框测试</h1>
    
    <div class="test-section">
        <h3>测试1: 基本多选</h3>
        <select id="test1" multiple style="width: 300px;">
            <option value="option1">选项1</option>
            <option value="option2">选项2</option>
            <option value="option3">选项3</option>
            <option value="option4">选项4</option>
            <option value="option5">选项5</option>
        </select>
    </div>
    
    <div class="test-section">
        <h3>测试2: 带预选值的多选</h3>
        <select id="test2" multiple style="width: 300px;">
            <option value="option1" selected>选项1</option>
            <option value="option2" selected>选项2</option>
            <option value="option3">选项3</option>
            <option value="option4">选项4</option>
            <option value="option5">选项5</option>
        </select>
    </div>
    
    <div class="test-section">
        <h3>测试3: 禁用选项的多选</h3>
        <select id="test3" multiple style="width: 300px;">
            <option value="option1" selected>选项1</option>
            <option value="option2" selected disabled>选项2 (禁用)</option>
            <option value="option3">选项3</option>
            <option value="option4">选项4</option>
            <option value="option5">选项5</option>
        </select>
    </div>

    <script>
        $(document).ready(function() {
            // 初始化所有测试选择器
            $('#test1').chosen({
                placeholder_text_multiple: "请选择选项..."
            });
            
            $('#test2').chosen({
                placeholder_text_multiple: "请选择选项..."
            });
            
            $('#test3').chosen({
                placeholder_text_multiple: "请选择选项..."
            });
            
            // 添加调试信息
            console.log('Chosen 初始化完成');
            
            // 检查关闭按钮的点击事件
            $(document).on('click', '.search-choice-close', function(e) {
                console.log('关闭按钮被点击', e);
            });
        });
    </script>
</body>
</html>
